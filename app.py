import os, base64, io, json
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from dotenv import load_dotenv
from azure.cosmos import CosmosClient
from PyPDF2 import PdfReader
import docx
from pptx import Presentation
import requests
import openai  # pip install openai

# ----------------- Load env vars -----------------
load_dotenv()

COSMOS_ENDPOINT = os.getenv("COSMOS_URL")
COSMOS_KEY = os.getenv("COSMOS_KEY")
DATABASE_NAME = os.getenv("DATABASE_NAME")
CONTAINER_NAME = os.getenv("CONTAINER_NAME")

AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_EMBEDDING_MODEL = os.getenv("AZURE_OPENAI_EMBEDDING_MODEL")
AZURE_OPENAI_DEPLOYMENT = os.getenv("AZURE_OPENAI_DEPLOYMENT")

# Configure Azure OpenAI client


# ----------------- Cosmos DB client -----------------
cosmos_client = CosmosClient(COSMOS_ENDPOINT, COSMOS_KEY)
db = cosmos_client.create_database_if_not_exists(id=DATABASE_NAME)
container = db.create_container_if_not_exists(id=CONTAINER_NAME, partition_key="/id")

# ----------------- FastAPI -----------------
app = FastAPI()

class CopilotRequest(BaseModel):
    copilot_url: str

# ---------- File extractors ----------
def extract_text_from_pdf(data: bytes) -> str:
    reader = PdfReader(io.BytesIO(data))
    return "\n".join([page.extract_text() for page in reader.pages if page.extract_text()])

def extract_text_from_docx(data: bytes) -> str:
    document = docx.Document(io.BytesIO(data))
    return "\n".join([para.text for para in document.paragraphs])

def extract_text_from_pptx(data: bytes) -> str:
    prs = Presentation(io.BytesIO(data))
    texts = []
    for slide in prs.slides:
        for shape in slide.shapes:
            if hasattr(shape, "text"):
                texts.append(shape.text)
    return "\n".join(texts)

def extract_text_from_txt(data: bytes) -> str:
    return data.decode("utf-8", errors="ignore")

def extract_text_from_json(data: bytes) -> str:
    return json.dumps(json.loads(data.decode("utf-8")), indent=2)

def extract_text_from_file(data: bytes, filename: str) -> str:
    ext = filename.lower().split(".")[-1]
    if ext == "pdf": return extract_text_from_pdf(data)
    if ext == "docx": return extract_text_from_docx(data)
    if ext == "pptx": return extract_text_from_pptx(data)
    if ext == "txt": return extract_text_from_txt(data)
    if ext == "json": return extract_text_from_json(data)
    return "<Unsupported file type>"

from openai import OpenAI

# Initialize client
client = OpenAI(api_type="azure", api_base=AZURE_OPENAI_ENDPOINT, api_version="2023-07-01-preview", api_key=AZURE_OPENAI_API_KEY)

def get_embedding(text: str) -> list[float]:
    response = client.embeddings.create(
        model=AZURE_OPENAI_EMBEDDING_MODEL,
        input=text
    )
    return response.data[0].embedding

# ---------- API endpoint ----------
@app.post("/process-from-copilot")
async def process_from_copilot(request: CopilotRequest):
    try:
        res = requests.get(request.copilot_url)
        res.raise_for_status()
        copilot_data = res.json()
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to fetch Copilot data: {e}")

    # Adjust key to match your mock API
    base64_str = copilot_data.get("basse64_data")
    message_id = copilot_data.get("message_id")
    
    if not base64_str:
        raise HTTPException(status_code=400, detail="Missing base64 data from Copilot")

    try:
        file_bytes = base64.b64decode(base64_str)
        # Auto-detect file type if needed; here assuming PDF
        extracted_text = extract_text_from_pdf(file_bytes)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Base64 decoding or text extraction failed: {e}")

    # Generate embedding via Azure OpenAI
    try:
        embedding = get_embedding(extracted_text)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate embedding: {e}")

    doc_id = message_id or "unknown_message"

    # Store in Cosmos DB
    try:
        container.upsert_item({
            "id": doc_id,
            "message_id": message_id,
            "content": extracted_text,
            "embedding": embedding,
            "file_type": "pdf"
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to store in Cosmos DB: {e}")

    return {
        "status": "success",
        "message_id": message_id,
        "cosmos_id": doc_id,
        "content_preview": extracted_text[:200] + "..." if len(extracted_text) > 200 else extracted_text
    }
